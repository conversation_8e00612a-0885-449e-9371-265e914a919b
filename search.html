<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="重庆锦雨丰建筑有限公司 - 站内搜索，快速找到您需要的信息">
    <meta name="keywords" content="站内搜索,搜索,重庆锦雨丰">
    <title>站内搜索 - 重庆锦雨丰建筑有限公司</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/logo/logo-black.png">
    
    <!-- Bootstrap CSS -->
    <link href="libs/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link href="libs/fontawesome.min.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top shadow-sm">
        <div class="container">
            <!-- 品牌Logo -->
            <a class="navbar-brand" href="index.html">
                <img src="images/logo/logo-black.png" alt="重庆锦雨丰建筑有限公司" class="d-inline-block align-text-top">
            </a>
            
            <!-- 移动端切换按钮 -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- 导航菜单 -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">网站首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="glass-curtain-wall.html">玻璃幕墙</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="aluminium-window.html">铝合金门窗</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="cases.html">成功案例</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="videos.html">视频中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="news.html">资讯动态</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">联系我们</a>
                    </li>
                </ul>
                
                <!-- 右侧按钮组 -->
                <div class="d-flex align-items-center gap-2">
                    <!-- 主题切换按钮 -->
                    <button class="theme-toggle" id="themeToggle" title="切换主题">
                        <i class="fas fa-sun" id="lightIcon"></i>
                        <i class="fas fa-moon d-none" id="darkIcon"></i>
                    </button>
                    
                    <!-- 联系我们按钮 -->
                    <a href="contact.html" class="btn btn-primary btn-sm">
                        <i class="fas fa-phone me-1"></i>联系我们
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="pt-5 mt-4">
        <!-- 页面标题区域 -->
        <section class="py-5 bg-gradient-primary text-white">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <nav aria-label="breadcrumb" class="mb-3">
                            <ol class="breadcrumb text-white-50">
                                <li class="breadcrumb-item"><a href="index.html" class="text-white-50">首页</a></li>
                                <li class="breadcrumb-item active text-white">站内搜索</li>
                            </ol>
                        </nav>
                        <h1 class="display-4 fw-bold mb-3">站内搜索</h1>
                        <p class="lead mb-0">快速找到您需要的信息</p>
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <i class="fas fa-search fa-5x opacity-75"></i>
                    </div>
                </div>
            </div>
        </section>

        <!-- 搜索区域 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="card border-0 shadow-lg">
                            <div class="card-body p-5">
                                <div class="text-center mb-4">
                                    <i class="fas fa-search fa-3x text-primary mb-3"></i>
                                    <h3 class="fw-bold">搜索我们的网站</h3>
                                    <p class="text-muted">输入关键词搜索产品、服务、案例或资讯</p>
                                </div>
                                
                                <form id="searchForm">
                                    <div class="input-group input-group-lg mb-4">
                                        <input type="text" class="form-control" placeholder="请输入搜索关键词..." id="searchInput" autofocus>
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search me-2"></i>搜索
                                        </button>
                                    </div>
                                </form>

                                <!-- 搜索建议 -->
                                <div class="mb-4">
                                    <h6 class="fw-bold mb-3">热门搜索</h6>
                                    <div class="d-flex flex-wrap gap-2">
                                        <button class="btn btn-outline-primary btn-sm search-tag" data-keyword="玻璃幕墙">玻璃幕墙</button>
                                        <button class="btn btn-outline-primary btn-sm search-tag" data-keyword="铝合金门窗">铝合金门窗</button>
                                        <button class="btn btn-outline-primary btn-sm search-tag" data-keyword="成功案例">成功案例</button>
                                        <button class="btn btn-outline-primary btn-sm search-tag" data-keyword="施工工艺">施工工艺</button>
                                        <button class="btn btn-outline-primary btn-sm search-tag" data-keyword="节能门窗">节能门窗</button>
                                        <button class="btn btn-outline-primary btn-sm search-tag" data-keyword="幕墙设计">幕墙设计</button>
                                        <button class="btn btn-outline-primary btn-sm search-tag" data-keyword="建筑装饰">建筑装饰</button>
                                        <button class="btn btn-outline-primary btn-sm search-tag" data-keyword="工程案例">工程案例</button>
                                    </div>
                                </div>

                                <!-- 搜索分类 -->
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <h6 class="fw-bold mb-3">按分类搜索</h6>
                                        <div class="list-group">
                                            <a href="#" class="list-group-item list-group-item-action search-category" data-category="products">
                                                <i class="fas fa-cube me-2"></i>产品服务
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action search-category" data-category="cases">
                                                <i class="fas fa-images me-2"></i>成功案例
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action search-category" data-category="news">
                                                <i class="fas fa-newspaper me-2"></i>资讯动态
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action search-category" data-category="videos">
                                                <i class="fas fa-video me-2"></i>视频中心
                                            </a>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="fw-bold mb-3">快速导航</h6>
                                        <div class="list-group">
                                            <a href="glass-curtain-wall.html" class="list-group-item list-group-item-action">
                                                <i class="fas fa-building me-2"></i>玻璃幕墙
                                            </a>
                                            <a href="aluminium-window.html" class="list-group-item list-group-item-action">
                                                <i class="fas fa-window-maximize me-2"></i>铝合金门窗
                                            </a>
                                            <a href="about.html" class="list-group-item list-group-item-action">
                                                <i class="fas fa-info-circle me-2"></i>关于我们
                                            </a>
                                            <a href="contact.html" class="list-group-item list-group-item-action">
                                                <i class="fas fa-phone me-2"></i>联系我们
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 搜索结果区域 -->
        <section class="py-5 bg-light" id="searchResults" style="display: none;">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4 class="fw-bold mb-0">搜索结果</h4>
                            <span class="text-muted" id="resultCount">找到 0 个结果</span>
                        </div>
                        
                        <!-- 搜索结果列表 -->
                        <div id="resultsList">
                            <!-- 搜索结果将通过JavaScript动态生成 -->
                        </div>

                        <!-- 无结果提示 -->
                        <div id="noResults" class="text-center py-5" style="display: none;">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">未找到相关结果</h5>
                            <p class="text-muted">请尝试使用其他关键词或查看下面的建议</p>
                            <div class="mt-4">
                                <a href="glass-curtain-wall.html" class="btn btn-outline-primary me-2">玻璃幕墙</a>
                                <a href="aluminium-window.html" class="btn btn-outline-primary me-2">铝合金门窗</a>
                                <a href="cases.html" class="btn btn-outline-primary me-2">成功案例</a>
                                <a href="contact.html" class="btn btn-outline-primary">联系我们</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3">重庆锦雨丰建筑有限公司</h5>
                    <p class="text-muted">致力于为客户提供高品质建筑服务的综合性企业，专业从事玻璃幕墙和铝合金门窗的设计、制造与安装。</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-weixin"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-qq"></i></a>
                        <a href="#" class="text-light"><i class="fas fa-phone"></i></a>
                        <a href="#" class="text-light"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-bold mb-3">产品服务</h6>
                    <ul class="list-unstyled">
                        <li><a href="glass-curtain-wall.html" class="text-muted text-decoration-none">玻璃幕墙</a></li>
                        <li><a href="aluminium-window.html" class="text-muted text-decoration-none">铝合金门窗</a></li>
                        <li><a href="cases.html" class="text-muted text-decoration-none">成功案例</a></li>
                        <li><a href="videos.html" class="text-muted text-decoration-none">视频中心</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-bold mb-3">公司信息</h6>
                    <ul class="list-unstyled">
                        <li><a href="about.html" class="text-muted text-decoration-none">关于我们</a></li>
                        <li><a href="news.html" class="text-muted text-decoration-none">资讯动态</a></li>
                        <li><a href="contact.html" class="text-muted text-decoration-none">联系我们</a></li>
                        <li><a href="search.html" class="text-muted text-decoration-none">站内搜索</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h6 class="fw-bold mb-3">联系方式</h6>
                    <ul class="list-unstyled text-muted">
                        <li><i class="fas fa-map-marker-alt me-2"></i>重庆市渝北区某某街道123号</li>
                        <li><i class="fas fa-phone me-2"></i>023-12345678</li>
                        <li><i class="fas fa-mobile-alt me-2"></i>138-0000-0000</li>
                        <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 重庆锦雨丰建筑有限公司. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex gap-3 justify-content-md-end">
                        <a href="#" class="text-muted text-decoration-none small">隐私政策</a>
                        <a href="#" class="text-muted text-decoration-none small">服务条款</a>
                        <a href="#" class="text-muted text-decoration-none small">网站地图</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 回到顶部按钮 -->
    <button class="btn btn-primary back-to-top" id="backToTop" title="回到顶部">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Bootstrap JavaScript -->
    <script src="libs/bootstrap.bundle.min.js"></script>
    <!-- 自定义JavaScript -->
    <script src="js/main.js"></script>

    <!-- 搜索页面功能 -->
    <script>
        // 模拟搜索数据库
        const searchDatabase = [
            {
                title: "玻璃幕墙专业设计与施工",
                url: "glass-curtain-wall.html",
                description: "专业的玻璃幕墙设计与施工服务，包括框架式、单元式、点支式幕墙",
                category: "products",
                keywords: ["玻璃幕墙", "幕墙设计", "幕墙施工", "框架式", "单元式", "点支式"]
            },
            {
                title: "铝合金门窗定制服务",
                url: "aluminium-window.html",
                description: "高品质铝合金门窗定制，节能环保，安全可靠",
                category: "products",
                keywords: ["铝合金门窗", "门窗定制", "节能门窗", "断桥铝", "系统门窗"]
            },
            {
                title: "重庆某商业综合体幕墙工程",
                url: "case-detail.html?id=1",
                description: "大型商业综合体玻璃幕墙工程案例，展示专业施工工艺",
                category: "cases",
                keywords: ["商业综合体", "幕墙工程", "案例", "施工工艺"]
            },
            {
                title: "关于重庆锦雨丰建筑有限公司",
                url: "about.html",
                description: "了解公司发展历程、企业文化、团队介绍等信息",
                category: "company",
                keywords: ["公司介绍", "企业文化", "发展历程", "团队"]
            },
            {
                title: "建筑幕墙行业发展新趋势",
                url: "news-detail.html?id=1",
                description: "分析建筑幕墙行业发展趋势，探讨绿色节能技术应用",
                category: "news",
                keywords: ["行业趋势", "绿色节能", "技术发展", "建筑幕墙"]
            },
            {
                title: "公司宣传片视频",
                url: "videos.html",
                description: "观看公司宣传片，了解企业实力和项目成果",
                category: "videos",
                keywords: ["宣传片", "视频", "企业实力", "项目成果"]
            }
        ];

        document.addEventListener('DOMContentLoaded', function() {
            const searchForm = document.getElementById('searchForm');
            const searchInput = document.getElementById('searchInput');
            const searchResults = document.getElementById('searchResults');
            const resultsList = document.getElementById('resultsList');
            const resultCount = document.getElementById('resultCount');
            const noResults = document.getElementById('noResults');

            // 获取URL参数中的搜索词
            const urlParams = new URLSearchParams(window.location.search);
            const initialQuery = urlParams.get('q');
            if (initialQuery) {
                searchInput.value = initialQuery;
                performSearch(initialQuery);
            }

            // 搜索表单提交
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const query = searchInput.value.trim();
                if (query) {
                    performSearch(query);
                }
            });

            // 热门搜索标签点击
            document.querySelectorAll('.search-tag').forEach(tag => {
                tag.addEventListener('click', function() {
                    const keyword = this.getAttribute('data-keyword');
                    searchInput.value = keyword;
                    performSearch(keyword);
                });
            });

            // 分类搜索点击
            document.querySelectorAll('.search-category').forEach(category => {
                category.addEventListener('click', function(e) {
                    e.preventDefault();
                    const categoryType = this.getAttribute('data-category');
                    performCategorySearch(categoryType);
                });
            });

            // 执行搜索
            function performSearch(query) {
                const results = searchInDatabase(query);
                displayResults(results, query);
            }

            // 分类搜索
            function performCategorySearch(category) {
                const results = searchDatabase.filter(item => item.category === category);
                displayResults(results, `分类：${getCategoryName(category)}`);
            }

            // 在数据库中搜索
            function searchInDatabase(query) {
                const lowerQuery = query.toLowerCase();
                return searchDatabase.filter(item => {
                    return item.title.toLowerCase().includes(lowerQuery) ||
                           item.description.toLowerCase().includes(lowerQuery) ||
                           item.keywords.some(keyword => keyword.toLowerCase().includes(lowerQuery));
                });
            }

            // 显示搜索结果
            function displayResults(results, query) {
                searchResults.style.display = 'block';
                resultCount.textContent = `找到 ${results.length} 个结果`;

                if (results.length === 0) {
                    resultsList.innerHTML = '';
                    noResults.style.display = 'block';
                } else {
                    noResults.style.display = 'none';
                    resultsList.innerHTML = results.map(result => createResultItem(result, query)).join('');
                }

                // 滚动到结果区域
                searchResults.scrollIntoView({ behavior: 'smooth' });
            }

            // 创建结果项HTML
            function createResultItem(result, query) {
                const categoryIcon = getCategoryIcon(result.category);
                const categoryName = getCategoryName(result.category);

                return `
                    <div class="card border-0 shadow-sm mb-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title fw-bold mb-0">
                                    <a href="${result.url}" class="text-decoration-none">${highlightText(result.title, query)}</a>
                                </h6>
                                <span class="badge bg-primary">
                                    <i class="${categoryIcon} me-1"></i>${categoryName}
                                </span>
                            </div>
                            <p class="card-text text-muted small mb-2">${highlightText(result.description, query)}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">${result.url}</small>
                                <a href="${result.url}" class="btn btn-outline-primary btn-sm">查看详情</a>
                            </div>
                        </div>
                    </div>
                `;
            }

            // 高亮搜索关键词
            function highlightText(text, query) {
                if (!query) return text;
                const regex = new RegExp(`(${query})`, 'gi');
                return text.replace(regex, '<mark>$1</mark>');
            }

            // 获取分类图标
            function getCategoryIcon(category) {
                const icons = {
                    'products': 'fas fa-cube',
                    'cases': 'fas fa-images',
                    'news': 'fas fa-newspaper',
                    'videos': 'fas fa-video',
                    'company': 'fas fa-building'
                };
                return icons[category] || 'fas fa-file';
            }

            // 获取分类名称
            function getCategoryName(category) {
                const names = {
                    'products': '产品服务',
                    'cases': '成功案例',
                    'news': '资讯动态',
                    'videos': '视频中心',
                    'company': '公司信息'
                };
                return names[category] || '其他';
            }
        });
    </script>
</body>
</html>
