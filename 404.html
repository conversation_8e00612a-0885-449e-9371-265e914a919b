<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="页面未找到 - 重庆锦雨丰建筑有限公司">
    <meta name="keywords" content="404,页面未找到,重庆锦雨丰">
    <title>页面未找到 - 重庆锦雨丰建筑有限公司</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/logo/logo-black.png">
    
    <!-- Bootstrap CSS -->
    <link href="libs/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link href="libs/fontawesome.min.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <link href="css/style.css" rel="stylesheet">
    
    <style>
        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .error-content {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 2rem;
        }
        .error-number {
            font-size: 8rem;
            font-weight: bold;
            line-height: 1;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .error-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.8;
        }
        .search-suggestions {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
        }
        .suggestion-item {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .suggestion-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }
        .shape:nth-child(3) {
            bottom: 10%;
            left: 20%;
            animation-delay: 4s;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <!-- 浮动装饰元素 -->
        <div class="floating-shapes">
            <div class="shape">
                <i class="fas fa-building fa-3x"></i>
            </div>
            <div class="shape">
                <i class="fas fa-tools fa-2x"></i>
            </div>
            <div class="shape">
                <i class="fas fa-home fa-4x"></i>
            </div>
        </div>

        <div class="error-content">
            <!-- 错误图标 -->
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <!-- 404数字 -->
            <div class="error-number">404</div>
            
            <!-- 错误信息 -->
            <h1 class="h2 mb-3">页面未找到</h1>
            <p class="lead mb-4">
                抱歉，您访问的页面不存在或已被移动。<br>
                请检查网址是否正确，或者尝试以下建议。
            </p>

            <!-- 快速导航按钮 -->
            <div class="d-flex gap-3 justify-content-center mb-4 flex-wrap">
                <a href="index.html" class="btn btn-light btn-lg">
                    <i class="fas fa-home me-2"></i>返回首页
                </a>
                <a href="javascript:history.back()" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>返回上页
                </a>
                <a href="contact.html" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-phone me-2"></i>联系我们
                </a>
            </div>

            <!-- 搜索建议 -->
            <div class="search-suggestions">
                <h4 class="mb-4">
                    <i class="fas fa-lightbulb me-2"></i>您可能在寻找
                </h4>
                
                <div class="row g-3">
                    <div class="col-md-6">
                        <a href="glass-curtain-wall.html" class="suggestion-item d-block text-decoration-none text-white">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-building me-3 fa-2x"></i>
                                <div>
                                    <h6 class="mb-1">玻璃幕墙</h6>
                                    <small class="opacity-75">专业幕墙设计与施工</small>
                                </div>
                            </div>
                        </a>
                    </div>
                    
                    <div class="col-md-6">
                        <a href="aluminium-window.html" class="suggestion-item d-block text-decoration-none text-white">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-window-maximize me-3 fa-2x"></i>
                                <div>
                                    <h6 class="mb-1">铝合金门窗</h6>
                                    <small class="opacity-75">高品质门窗定制服务</small>
                                </div>
                            </div>
                        </a>
                    </div>
                    
                    <div class="col-md-6">
                        <a href="cases.html" class="suggestion-item d-block text-decoration-none text-white">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-images me-3 fa-2x"></i>
                                <div>
                                    <h6 class="mb-1">成功案例</h6>
                                    <small class="opacity-75">查看我们的项目案例</small>
                                </div>
                            </div>
                        </a>
                    </div>
                    
                    <div class="col-md-6">
                        <a href="about.html" class="suggestion-item d-block text-decoration-none text-white">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle me-3 fa-2x"></i>
                                <div>
                                    <h6 class="mb-1">关于我们</h6>
                                    <small class="opacity-75">了解公司信息</small>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- 搜索框 -->
                <div class="mt-4">
                    <h6 class="mb-3">或者搜索您需要的内容</h6>
                    <div class="input-group">
                        <input type="text" class="form-control form-control-lg" placeholder="输入关键词搜索..." id="searchInput">
                        <button class="btn btn-light" type="button" id="searchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="mt-4">
                <p class="mb-2">
                    <i class="fas fa-phone me-2"></i>
                    如需帮助，请致电：<a href="tel:023-12345678" class="text-white">023-12345678</a>
                </p>
                <p class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    或发送邮件至：<a href="mailto:<EMAIL>" class="text-white"><EMAIL></a>
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="libs/bootstrap.bundle.min.js"></script>
    <!-- 自定义JavaScript -->
    <script src="js/main.js"></script>

    <!-- 404页面功能 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 搜索功能
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');

            function performSearch() {
                const searchTerm = searchInput.value.trim();
                if (searchTerm) {
                    // 重定向到搜索页面或首页
                    window.location.href = `search.html?q=${encodeURIComponent(searchTerm)}`;
                }
            }

            searchBtn.addEventListener('click', performSearch);
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // 记录404错误（用于分析）
            if (typeof gtag !== 'undefined') {
                gtag('event', 'page_view', {
                    page_title: '404 Error',
                    page_location: window.location.href
                });
            }

            // 自动建议功能
            const suggestions = [
                { text: '玻璃幕墙', url: 'glass-curtain-wall.html' },
                { text: '铝合金门窗', url: 'aluminium-window.html' },
                { text: '成功案例', url: 'cases.html' },
                { text: '关于我们', url: 'about.html' },
                { text: '联系我们', url: 'contact.html' },
                { text: '资讯动态', url: 'news.html' },
                { text: '视频中心', url: 'videos.html' }
            ];

            // 根据当前URL尝试智能建议
            const currentPath = window.location.pathname.toLowerCase();
            suggestions.forEach(suggestion => {
                if (currentPath.includes(suggestion.text) || currentPath.includes(suggestion.url.replace('.html', ''))) {
                    // 高亮相关建议
                    const suggestionElements = document.querySelectorAll('.suggestion-item');
                    suggestionElements.forEach(el => {
                        if (el.href.includes(suggestion.url)) {
                            el.style.background = 'rgba(255,255,255,0.3)';
                            el.style.border = '2px solid rgba(255,255,255,0.5)';
                        }
                    });
                }
            });

            // 添加动画效果
            const errorContent = document.querySelector('.error-content');
            errorContent.style.opacity = '0';
            errorContent.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                errorContent.style.transition = 'all 0.8s ease';
                errorContent.style.opacity = '1';
                errorContent.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
