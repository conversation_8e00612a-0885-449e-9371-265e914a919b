<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="重庆锦雨丰建筑有限公司 - 联系我们，获取专业的建筑服务咨询">
    <meta name="keywords" content="联系我们,联系方式,地址,电话,重庆锦雨丰">
    <title>联系我们 - 重庆锦雨丰建筑有限公司</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/logo/logo-black.png">
    
    <!-- Bootstrap CSS -->
    <link href="libs/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link href="libs/fontawesome.min.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top shadow-sm">
        <div class="container">
            <!-- 品牌Logo -->
            <a class="navbar-brand" href="index.html">
                <img src="images/logo/logo-black.png" alt="重庆锦雨丰建筑有限公司" class="d-inline-block align-text-top">
            </a>
            
            <!-- 移动端切换按钮 -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- 导航菜单 -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">网站首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="glass-curtain-wall.html">玻璃幕墙</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="aluminium-window.html">铝合金门窗</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="cases.html">成功案例</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="videos.html">视频中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="news.html">资讯动态</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.html">联系我们</a>
                    </li>
                </ul>
                
                <!-- 右侧按钮组 -->
                <div class="d-flex align-items-center gap-2">
                    <!-- 主题切换按钮 -->
                    <button class="theme-toggle" id="themeToggle" title="切换主题">
                        <i class="fas fa-sun" id="lightIcon"></i>
                        <i class="fas fa-moon d-none" id="darkIcon"></i>
                    </button>
                    
                    <!-- 联系我们按钮 -->
                    <a href="contact.html" class="btn btn-primary btn-sm">
                        <i class="fas fa-phone me-1"></i>联系我们
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="pt-5 mt-4">
        <!-- 页面标题区域 -->
        <section class="py-5 bg-gradient-primary text-white">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <nav aria-label="breadcrumb" class="mb-3">
                            <ol class="breadcrumb text-white-50">
                                <li class="breadcrumb-item"><a href="index.html" class="text-white-50">首页</a></li>
                                <li class="breadcrumb-item active text-white">联系我们</li>
                            </ol>
                        </nav>
                        <h1 class="display-4 fw-bold mb-3">联系我们</h1>
                        <p class="lead mb-0">我们期待与您的合作，为您提供专业的建筑服务</p>
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <img src="images/contact/contact-banner.jpg" alt="联系我们" class="img-fluid rounded shadow" style="max-height: 200px;">
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系信息 -->
        <section class="py-5">
            <div class="container">
                <div class="row g-4">
                    <!-- 联系方式 -->
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center p-4">
                                <div class="icon-circle bg-primary text-white mb-3 mx-auto">
                                    <i class="fas fa-phone fa-2x"></i>
                                </div>
                                <h5 class="fw-bold mb-3">电话咨询</h5>
                                <p class="text-muted mb-3">工作时间：周一至周六 8:00-18:00</p>
                                <div class="d-grid gap-2">
                                    <a href="tel:023-12345678" class="btn btn-primary">
                                        <i class="fas fa-phone me-2"></i>023-12345678
                                    </a>
                                    <a href="tel:138-0000-0000" class="btn btn-outline-primary">
                                        <i class="fas fa-mobile-alt me-2"></i>138-0000-0000
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 邮箱联系 -->
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center p-4">
                                <div class="icon-circle bg-success text-white mb-3 mx-auto">
                                    <i class="fas fa-envelope fa-2x"></i>
                                </div>
                                <h5 class="fw-bold mb-3">邮箱联系</h5>
                                <p class="text-muted mb-3">我们会在24小时内回复您的邮件</p>
                                <div class="d-grid gap-2">
                                    <a href="mailto:<EMAIL>" class="btn btn-success">
                                        <i class="fas fa-envelope me-2"></i><EMAIL>
                                    </a>
                                    <a href="mailto:<EMAIL>" class="btn btn-outline-success">
                                        <i class="fas fa-envelope me-2"></i><EMAIL>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 地址信息 -->
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center p-4">
                                <div class="icon-circle bg-info text-white mb-3 mx-auto">
                                    <i class="fas fa-map-marker-alt fa-2x"></i>
                                </div>
                                <h5 class="fw-bold mb-3">公司地址</h5>
                                <p class="text-muted mb-3">欢迎您到公司实地考察</p>
                                <div class="d-grid">
                                    <button class="btn btn-info" onclick="showMap()">
                                        <i class="fas fa-map me-2"></i>重庆市渝北区某某街道123号
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 在线留言 -->
        <section class="py-5 bg-light">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-primary text-white text-center">
                                <h4 class="mb-0"><i class="fas fa-comment me-2"></i>在线留言</h4>
                                <small>请填写以下信息，我们会尽快与您联系</small>
                            </div>
                            <div class="card-body p-4">
                                <form id="contactForm">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="name" class="form-label">姓名 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="phone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                            <input type="tel" class="form-control" id="phone" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="email" class="form-label">邮箱地址</label>
                                            <input type="email" class="form-control" id="email">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="company" class="form-label">公司名称</label>
                                            <input type="text" class="form-control" id="company">
                                        </div>
                                        <div class="col-12">
                                            <label for="service" class="form-label">咨询服务</label>
                                            <select class="form-select" id="service">
                                                <option value="">请选择咨询服务</option>
                                                <option value="glass-curtain-wall">玻璃幕墙</option>
                                                <option value="aluminium-window">铝合金门窗</option>
                                                <option value="design">设计咨询</option>
                                                <option value="installation">安装服务</option>
                                                <option value="maintenance">维护保养</option>
                                                <option value="other">其他服务</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <label for="message" class="form-label">留言内容 <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="message" rows="5" placeholder="请详细描述您的需求..." required></textarea>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="privacy" required>
                                                <label class="form-check-label" for="privacy">
                                                    我已阅读并同意 <a href="#" class="text-primary">隐私政策</a>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-12 text-center">
                                            <button type="submit" class="btn btn-primary btn-lg px-5">
                                                <i class="fas fa-paper-plane me-2"></i>提交留言
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 地图区域 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-map me-2"></i>公司位置</h5>
                            </div>
                            <div class="card-body p-0">
                                <!-- 百度地图容器 -->
                                <div id="baiduMap" style="height: 400px; width: 100%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 工作时间和交通指南 -->
        <section class="py-5 bg-light">
            <div class="container">
                <div class="row g-4">
                    <div class="col-lg-6">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fas fa-clock me-2"></i>工作时间</h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-6">
                                        <strong>周一至周五</strong>
                                        <div class="text-muted">8:00 - 18:00</div>
                                    </div>
                                    <div class="col-6">
                                        <strong>周六</strong>
                                        <div class="text-muted">9:00 - 17:00</div>
                                    </div>
                                    <div class="col-6">
                                        <strong>周日</strong>
                                        <div class="text-muted">休息</div>
                                    </div>
                                    <div class="col-6">
                                        <strong>法定节假日</strong>
                                        <div class="text-muted">休息</div>
                                    </div>
                                </div>
                                <hr>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    紧急情况请拨打24小时服务热线：138-0000-0000
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="mb-0"><i class="fas fa-route me-2"></i>交通指南</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong><i class="fas fa-subway me-2 text-primary"></i>地铁</strong>
                                    <div class="text-muted">3号线某某站下车，步行约10分钟</div>
                                </div>
                                <div class="mb-3">
                                    <strong><i class="fas fa-bus me-2 text-success"></i>公交</strong>
                                    <div class="text-muted">乘坐123路、456路至某某站下车</div>
                                </div>
                                <div class="mb-3">
                                    <strong><i class="fas fa-car me-2 text-warning"></i>自驾</strong>
                                    <div class="text-muted">导航至"重庆锦雨丰建筑有限公司"</div>
                                </div>
                                <div>
                                    <strong><i class="fas fa-parking me-2 text-info"></i>停车</strong>
                                    <div class="text-muted">公司提供免费停车位</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3">重庆锦雨丰建筑有限公司</h5>
                    <p class="text-muted">致力于为客户提供高品质建筑服务的综合性企业，专业从事玻璃幕墙和铝合金门窗的设计、制造与安装。</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-weixin"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-qq"></i></a>
                        <a href="#" class="text-light"><i class="fas fa-phone"></i></a>
                        <a href="#" class="text-light"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-bold mb-3">产品服务</h6>
                    <ul class="list-unstyled">
                        <li><a href="glass-curtain-wall.html" class="text-muted text-decoration-none">玻璃幕墙</a></li>
                        <li><a href="aluminium-window.html" class="text-muted text-decoration-none">铝合金门窗</a></li>
                        <li><a href="cases.html" class="text-muted text-decoration-none">成功案例</a></li>
                        <li><a href="videos.html" class="text-muted text-decoration-none">视频中心</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-bold mb-3">公司信息</h6>
                    <ul class="list-unstyled">
                        <li><a href="about.html" class="text-muted text-decoration-none">关于我们</a></li>
                        <li><a href="news.html" class="text-muted text-decoration-none">资讯动态</a></li>
                        <li><a href="contact.html" class="text-muted text-decoration-none">联系我们</a></li>
                        <li><a href="search.html" class="text-muted text-decoration-none">站内搜索</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h6 class="fw-bold mb-3">联系方式</h6>
                    <ul class="list-unstyled text-muted">
                        <li><i class="fas fa-map-marker-alt me-2"></i>重庆市渝北区某某街道123号</li>
                        <li><i class="fas fa-phone me-2"></i>023-12345678</li>
                        <li><i class="fas fa-mobile-alt me-2"></i>138-0000-0000</li>
                        <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 重庆锦雨丰建筑有限公司. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex gap-3 justify-content-md-end">
                        <a href="#" class="text-muted text-decoration-none small">隐私政策</a>
                        <a href="#" class="text-muted text-decoration-none small">服务条款</a>
                        <a href="#" class="text-muted text-decoration-none small">网站地图</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 回到顶部按钮 -->
    <button class="btn btn-primary back-to-top" id="backToTop" title="回到顶部">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- 百度地图API -->
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=YOUR_BAIDU_MAP_API_KEY"></script>

    <!-- Bootstrap JavaScript -->
    <script src="libs/bootstrap.bundle.min.js"></script>
    <!-- 自定义JavaScript -->
    <script src="js/main.js"></script>

    <!-- 联系页面功能 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化百度地图
            initBaiduMap();

            // 初始化联系表单
            initContactForm();
        });

        // 初始化百度地图
        function initBaiduMap() {
            // 检查百度地图API是否加载
            if (typeof BMap === 'undefined') {
                document.getElementById('baiduMap').innerHTML =
                    '<div class="d-flex align-items-center justify-content-center h-100 bg-light">' +
                    '<div class="text-center text-muted">' +
                    '<i class="fas fa-map fa-3x mb-3"></i>' +
                    '<p>地图加载中...</p>' +
                    '<small>如果地图无法显示，请检查网络连接</small>' +
                    '</div>' +
                    '</div>';
                return;
            }

            // 创建地图实例
            const map = new BMap.Map("baiduMap");

            // 设置公司位置（重庆市渝北区）
            const point = new BMap.Point(106.5516, 29.6016);
            map.centerAndZoom(point, 15);

            // 添加地图控件
            map.addControl(new BMap.MapTypeControl());
            map.addControl(new BMap.ScaleControl());
            map.addControl(new BMap.OverviewMapControl());
            map.addControl(new BMap.NavigationControl());

            // 创建标注
            const marker = new BMap.Marker(point);
            map.addOverlay(marker);

            // 创建信息窗口
            const infoWindow = new BMap.InfoWindow(
                '<div style="padding: 10px;">' +
                '<h6 class="fw-bold mb-2">重庆锦雨丰建筑有限公司</h6>' +
                '<p class="mb-1"><i class="fas fa-map-marker-alt me-2"></i>重庆市渝北区某某街道123号</p>' +
                '<p class="mb-1"><i class="fas fa-phone me-2"></i>023-12345678</p>' +
                '<p class="mb-0"><i class="fas fa-envelope me-2"></i><EMAIL></p>' +
                '</div>'
            );

            // 点击标注显示信息窗口
            marker.addEventListener("click", function() {
                map.openInfoWindow(infoWindow, point);
            });

            // 默认显示信息窗口
            map.openInfoWindow(infoWindow, point);
        }

        // 显示地图（点击地址按钮时调用）
        function showMap() {
            document.getElementById('baiduMap').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // 初始化联系表单
        function initContactForm() {
            const form = document.getElementById('contactForm');

            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // 获取表单数据
                const formData = {
                    name: document.getElementById('name').value,
                    phone: document.getElementById('phone').value,
                    email: document.getElementById('email').value,
                    company: document.getElementById('company').value,
                    service: document.getElementById('service').value,
                    message: document.getElementById('message').value
                };

                // 验证必填字段
                if (!formData.name || !formData.phone || !formData.message) {
                    alert('请填写所有必填字段！');
                    return;
                }

                // 验证电话号码格式
                const phoneRegex = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/;
                if (!phoneRegex.test(formData.phone)) {
                    alert('请输入正确的电话号码！');
                    return;
                }

                // 验证邮箱格式（如果填写了邮箱）
                if (formData.email) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(formData.email)) {
                        alert('请输入正确的邮箱地址！');
                        return;
                    }
                }

                // 模拟提交表单
                submitForm(formData);
            });
        }

        // 提交表单
        function submitForm(formData) {
            // 显示提交中状态
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
            submitBtn.disabled = true;

            // 模拟异步提交
            setTimeout(function() {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;

                // 显示成功消息
                alert('留言提交成功！我们会尽快与您联系。');

                // 重置表单
                document.getElementById('contactForm').reset();

                console.log('表单数据：', formData);
            }, 2000);
        }
    </script>
</body>
</html>
