<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="重庆锦雨丰建筑有限公司 - 视频中心，观看公司宣传片、施工过程和项目展示视频">
    <meta name="keywords" content="视频中心,公司宣传片,施工视频,项目展示,重庆锦雨丰">
    <title>视频中心 - 重庆锦雨丰建筑有限公司</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/logo/logo-black.png">
    
    <!-- Bootstrap CSS -->
    <link href="libs/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link href="libs/fontawesome.min.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top shadow-sm">
        <div class="container">
            <!-- 品牌Logo -->
            <a class="navbar-brand" href="index.html">
                <img src="images/logo/logo-black.png" alt="重庆锦雨丰建筑有限公司" class="d-inline-block align-text-top">
            </a>
            
            <!-- 移动端切换按钮 -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- 导航菜单 -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">网站首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="glass-curtain-wall.html">玻璃幕墙</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="aluminium-window.html">铝合金门窗</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="cases.html">成功案例</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="videos.html">视频中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="news.html">资讯动态</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">联系我们</a>
                    </li>
                </ul>
                
                <!-- 右侧按钮组 -->
                <div class="d-flex align-items-center gap-2">
                    <!-- 主题切换按钮 -->
                    <button class="theme-toggle" id="themeToggle" title="切换主题">
                        <i class="fas fa-sun" id="lightIcon"></i>
                        <i class="fas fa-moon d-none" id="darkIcon"></i>
                    </button>
                    
                    <!-- 联系我们按钮 -->
                    <a href="contact.html" class="btn btn-primary btn-sm">
                        <i class="fas fa-phone me-1"></i>联系我们
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="pt-5 mt-4">
        <!-- 页面标题区域 -->
        <section class="py-5 bg-gradient-primary text-white">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <nav aria-label="breadcrumb" class="mb-3">
                            <ol class="breadcrumb text-white-50">
                                <li class="breadcrumb-item"><a href="index.html" class="text-white-50">首页</a></li>
                                <li class="breadcrumb-item active text-white">视频中心</li>
                            </ol>
                        </nav>
                        <h1 class="display-4 fw-bold mb-3">视频中心</h1>
                        <p class="lead mb-0">观看我们的公司宣传片、施工过程和项目展示视频</p>
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <img src="images/videos/video-banner.jpg" alt="视频中心" class="img-fluid rounded shadow" style="max-height: 200px;">
                    </div>
                </div>
            </div>
        </section>

        <!-- 搜索和筛选区域 -->
        <section class="py-4 bg-light">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="搜索视频..." id="searchInput">
                            <button class="btn btn-primary" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-lg-6 mt-3 mt-lg-0">
                        <div class="d-flex gap-2 justify-content-lg-end">
                            <button class="btn btn-outline-primary active" data-filter="all">全部</button>
                            <button class="btn btn-outline-primary" data-filter="company">公司宣传</button>
                            <button class="btn btn-outline-primary" data-filter="construction">施工过程</button>
                            <button class="btn btn-outline-primary" data-filter="project">项目展示</button>
                            <button class="btn btn-outline-primary" data-filter="technology">技术介绍</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 特色视频 -->
        <section class="py-5">
            <div class="container">
                <h3 class="fw-bold mb-4">特色推荐</h3>
                <div class="row g-4">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm">
                            <div class="position-relative">
                                <img src="images/videos/featured-video.jpg" class="card-img-top" alt="公司宣传片" style="height: 400px; object-fit: cover;">
                                <div class="video-overlay">
                                    <button class="btn btn-primary btn-lg rounded-circle" data-bs-toggle="modal" data-bs-target="#videoModal" data-video="company-intro">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                                <div class="position-absolute top-0 start-0 m-3">
                                    <span class="badge bg-danger">推荐</span>
                                </div>
                                <div class="position-absolute bottom-0 end-0 m-3">
                                    <span class="badge bg-dark">05:32</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title fw-bold">重庆锦雨丰建筑有限公司宣传片</h5>
                                <p class="card-text text-muted">全面展示公司实力、团队风采和项目成果，了解我们的企业文化和发展历程。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>2024-06-15
                                        <i class="fas fa-eye ms-3 me-1"></i>1,256 次观看
                                    </small>
                                    <span class="badge bg-primary">公司宣传</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="card border-0 shadow-sm">
                                    <div class="position-relative">
                                        <img src="images/videos/video2.jpg" class="card-img-top" alt="施工工艺展示" style="height: 120px; object-fit: cover;">
                                        <div class="video-overlay-small">
                                            <button class="btn btn-primary btn-sm rounded-circle" data-bs-toggle="modal" data-bs-target="#videoModal" data-video="construction-process">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute bottom-0 end-0 m-2">
                                            <span class="badge bg-dark small">03:45</span>
                                        </div>
                                    </div>
                                    <div class="card-body p-3">
                                        <h6 class="card-title fw-bold mb-2">专业施工工艺展示</h6>
                                        <small class="text-muted">展示我们的专业施工工艺和严格的质量控制流程。</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="card border-0 shadow-sm">
                                    <div class="position-relative">
                                        <img src="images/videos/video3.jpg" class="card-img-top" alt="项目案例介绍" style="height: 120px; object-fit: cover;">
                                        <div class="video-overlay-small">
                                            <button class="btn btn-primary btn-sm rounded-circle" data-bs-toggle="modal" data-bs-target="#videoModal" data-video="project-showcase">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute bottom-0 end-0 m-2">
                                            <span class="badge bg-dark small">04:18</span>
                                        </div>
                                    </div>
                                    <div class="card-body p-3">
                                        <h6 class="card-title fw-bold mb-2">精品项目案例介绍</h6>
                                        <small class="text-muted">详细介绍我们完成的精品项目案例和技术特点。</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 视频列表 -->
        <section class="py-5 bg-light">
            <div class="container">
                <h3 class="fw-bold mb-4">全部视频</h3>
                <div class="row g-4" id="videosContainer">
                    <!-- 视频1 -->
                    <div class="col-lg-4 col-md-6 video-item" data-category="company">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <div class="position-relative">
                                <img src="images/videos/video1.jpg" class="card-img-top" alt="企业文化展示" style="height: 200px; object-fit: cover;">
                                <div class="video-overlay">
                                    <button class="btn btn-primary rounded-circle" data-bs-toggle="modal" data-bs-target="#videoModal" data-video="company-culture">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                                <div class="position-absolute bottom-0 end-0 m-3">
                                    <span class="badge bg-dark">02:45</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-primary">公司宣传</span>
                                    <small class="text-muted">2024-06-10</small>
                                </div>
                                <h6 class="card-title fw-bold">企业文化展示</h6>
                                <p class="card-text text-muted small">展示公司的企业文化、团队精神和工作环境。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>856 次观看
                                    </small>
                                    <a href="video-detail.html?id=1" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 视频2 -->
                    <div class="col-lg-4 col-md-6 video-item" data-category="construction">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <div class="position-relative">
                                <img src="images/videos/video4.jpg" class="card-img-top" alt="玻璃幕墙安装过程" style="height: 200px; object-fit: cover;">
                                <div class="video-overlay">
                                    <button class="btn btn-primary rounded-circle" data-bs-toggle="modal" data-bs-target="#videoModal" data-video="curtain-wall-installation">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                                <div class="position-absolute bottom-0 end-0 m-3">
                                    <span class="badge bg-dark">06:12</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-success">施工过程</span>
                                    <small class="text-muted">2024-06-05</small>
                                </div>
                                <h6 class="card-title fw-bold">玻璃幕墙安装过程</h6>
                                <p class="card-text text-muted small">详细展示玻璃幕墙的专业安装工艺和质量控制。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>1,024 次观看
                                    </small>
                                    <a href="video-detail.html?id=2" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 视频3 -->
                    <div class="col-lg-4 col-md-6 video-item" data-category="project">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <div class="position-relative">
                                <img src="images/videos/video5.jpg" class="card-img-top" alt="商业综合体项目" style="height: 200px; object-fit: cover;">
                                <div class="video-overlay">
                                    <button class="btn btn-primary rounded-circle" data-bs-toggle="modal" data-bs-target="#videoModal" data-video="commercial-project">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                                <div class="position-absolute bottom-0 end-0 m-3">
                                    <span class="badge bg-dark">04:56</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-info">项目展示</span>
                                    <small class="text-muted">2024-05-28</small>
                                </div>
                                <h6 class="card-title fw-bold">商业综合体项目展示</h6>
                                <p class="card-text text-muted small">展示大型商业综合体的幕墙设计和施工成果。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>1,345 次观看
                                    </small>
                                    <a href="video-detail.html?id=3" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 视频4 -->
                    <div class="col-lg-4 col-md-6 video-item" data-category="technology">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <div class="position-relative">
                                <img src="images/videos/video6.jpg" class="card-img-top" alt="节能门窗技术" style="height: 200px; object-fit: cover;">
                                <div class="video-overlay">
                                    <button class="btn btn-primary rounded-circle" data-bs-toggle="modal" data-bs-target="#videoModal" data-video="energy-saving-tech">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                                <div class="position-absolute bottom-0 end-0 m-3">
                                    <span class="badge bg-dark">03:28</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-warning">技术介绍</span>
                                    <small class="text-muted">2024-05-20</small>
                                </div>
                                <h6 class="card-title fw-bold">节能门窗技术介绍</h6>
                                <p class="card-text text-muted small">介绍我们的节能门窗技术和环保理念。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>678 次观看
                                    </small>
                                    <a href="video-detail.html?id=4" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 视频5 -->
                    <div class="col-lg-4 col-md-6 video-item" data-category="construction">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <div class="position-relative">
                                <img src="images/videos/video7.jpg" class="card-img-top" alt="铝合金门窗制作" style="height: 200px; object-fit: cover;">
                                <div class="video-overlay">
                                    <button class="btn btn-primary rounded-circle" data-bs-toggle="modal" data-bs-target="#videoModal" data-video="window-manufacturing">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                                <div class="position-absolute bottom-0 end-0 m-3">
                                    <span class="badge bg-dark">05:15</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-success">施工过程</span>
                                    <small class="text-muted">2024-05-15</small>
                                </div>
                                <h6 class="card-title fw-bold">铝合金门窗制作工艺</h6>
                                <p class="card-text text-muted small">展示铝合金门窗的精密制作工艺和质量检验。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>892 次观看
                                    </small>
                                    <a href="video-detail.html?id=5" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 视频6 -->
                    <div class="col-lg-4 col-md-6 video-item" data-category="project">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <div class="position-relative">
                                <img src="images/videos/video8.jpg" class="card-img-top" alt="住宅项目案例" style="height: 200px; object-fit: cover;">
                                <div class="video-overlay">
                                    <button class="btn btn-primary rounded-circle" data-bs-toggle="modal" data-bs-target="#videoModal" data-video="residential-project">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                                <div class="position-absolute bottom-0 end-0 m-3">
                                    <span class="badge bg-dark">04:02</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-info">项目展示</span>
                                    <small class="text-muted">2024-05-10</small>
                                </div>
                                <h6 class="card-title fw-bold">高档住宅项目案例</h6>
                                <p class="card-text text-muted small">展示高档住宅小区的门窗工程和居住体验。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>756 次观看
                                    </small>
                                    <a href="video-detail.html?id=6" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页导航 -->
                <nav aria-label="视频分页" class="mt-5">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <li class="page-item active">
                            <a class="page-link" href="#">1</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </section>
    </main>

    <!-- 视频模态框 -->
    <div class="modal fade" id="videoModal" tabindex="-1">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">视频播放</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="ratio ratio-16x9">
                        <iframe id="videoFrame" src="" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3">重庆锦雨丰建筑有限公司</h5>
                    <p class="text-muted">致力于为客户提供高品质建筑服务的综合性企业，专业从事玻璃幕墙和铝合金门窗的设计、制造与安装。</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-weixin"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-qq"></i></a>
                        <a href="#" class="text-light"><i class="fas fa-phone"></i></a>
                        <a href="#" class="text-light"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-bold mb-3">产品服务</h6>
                    <ul class="list-unstyled">
                        <li><a href="glass-curtain-wall.html" class="text-muted text-decoration-none">玻璃幕墙</a></li>
                        <li><a href="aluminium-window.html" class="text-muted text-decoration-none">铝合金门窗</a></li>
                        <li><a href="cases.html" class="text-muted text-decoration-none">成功案例</a></li>
                        <li><a href="videos.html" class="text-muted text-decoration-none">视频中心</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-bold mb-3">公司信息</h6>
                    <ul class="list-unstyled">
                        <li><a href="about.html" class="text-muted text-decoration-none">关于我们</a></li>
                        <li><a href="news.html" class="text-muted text-decoration-none">资讯动态</a></li>
                        <li><a href="contact.html" class="text-muted text-decoration-none">联系我们</a></li>
                        <li><a href="search.html" class="text-muted text-decoration-none">站内搜索</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h6 class="fw-bold mb-3">联系方式</h6>
                    <ul class="list-unstyled text-muted">
                        <li><i class="fas fa-map-marker-alt me-2"></i>重庆市渝北区某某街道123号</li>
                        <li><i class="fas fa-phone me-2"></i>023-12345678</li>
                        <li><i class="fas fa-mobile-alt me-2"></i>138-0000-0000</li>
                        <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 重庆锦雨丰建筑有限公司. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex gap-3 justify-content-md-end">
                        <a href="#" class="text-muted text-decoration-none small">隐私政策</a>
                        <a href="#" class="text-muted text-decoration-none small">服务条款</a>
                        <a href="#" class="text-muted text-decoration-none small">网站地图</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 回到顶部按钮 -->
    <button class="btn btn-primary back-to-top" id="backToTop" title="回到顶部">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Bootstrap JavaScript -->
    <script src="libs/bootstrap.bundle.min.js"></script>
    <!-- 自定义JavaScript -->
    <script src="js/main.js"></script>

    <!-- 视频中心功能 -->
    <script>
        // 视频数据映射
        const videoData = {
            'company-intro': 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'construction-process': 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'project-showcase': 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'company-culture': 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'curtain-wall-installation': 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'commercial-project': 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'energy-saving-tech': 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'window-manufacturing': 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'residential-project': 'https://www.youtube.com/embed/dQw4w9WgXcQ'
        };

        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('[data-filter]');
            const videoItems = document.querySelectorAll('.video-item');
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');
            const videoModal = document.getElementById('videoModal');
            const videoFrame = document.getElementById('videoFrame');

            // 筛选功能
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');

                    // 更新按钮状态
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // 筛选视频
                    videoItems.forEach(item => {
                        if (filter === 'all' || item.getAttribute('data-category').includes(filter)) {
                            item.style.display = 'block';
                            item.classList.add('fade-in-up');
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });

            // 搜索功能
            function performSearch() {
                const searchTerm = searchInput.value.toLowerCase();

                videoItems.forEach(item => {
                    const title = item.querySelector('.card-title').textContent.toLowerCase();
                    const description = item.querySelector('.card-text').textContent.toLowerCase();

                    if (title.includes(searchTerm) || description.includes(searchTerm)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            }

            searchBtn.addEventListener('click', performSearch);
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // 视频播放功能
            document.querySelectorAll('[data-video]').forEach(button => {
                button.addEventListener('click', function() {
                    const videoId = this.getAttribute('data-video');
                    const videoUrl = videoData[videoId];
                    if (videoUrl) {
                        videoFrame.src = videoUrl;
                    }
                });
            });

            // 模态框关闭时停止视频播放
            videoModal.addEventListener('hidden.bs.modal', function() {
                videoFrame.src = '';
            });
        });
    </script>
</body>
</html>
