<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="重庆锦雨丰建筑有限公司 - 成功案例展示，包括玻璃幕墙和铝合金门窗项目案例">
    <meta name="keywords" content="成功案例,项目案例,玻璃幕墙案例,铝合金门窗案例,重庆锦雨丰">
    <title>成功案例 - 重庆锦雨丰建筑有限公司</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/logo/logo-black.png">
    
    <!-- Bootstrap CSS -->
    <link href="libs/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link href="libs/fontawesome.min.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top shadow-sm">
        <div class="container">
            <!-- 品牌Logo -->
            <a class="navbar-brand" href="index.html">
                <img src="images/logo/logo-black.png" alt="重庆锦雨丰建筑有限公司" class="d-inline-block align-text-top">
            </a>
            
            <!-- 移动端切换按钮 -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- 导航菜单 -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">网站首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="glass-curtain-wall.html">玻璃幕墙</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="aluminium-window.html">铝合金门窗</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="cases.html">成功案例</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="videos.html">视频中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="news.html">资讯动态</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">联系我们</a>
                    </li>
                </ul>
                
                <!-- 右侧按钮组 -->
                <div class="d-flex align-items-center gap-2">
                    <!-- 主题切换按钮 -->
                    <button class="theme-toggle" id="themeToggle" title="切换主题">
                        <i class="fas fa-sun" id="lightIcon"></i>
                        <i class="fas fa-moon d-none" id="darkIcon"></i>
                    </button>
                    
                    <!-- 联系我们按钮 -->
                    <a href="contact.html" class="btn btn-primary btn-sm">
                        <i class="fas fa-phone me-1"></i>联系我们
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="pt-5 mt-4">
        <!-- 页面标题区域 -->
        <section class="py-5 bg-gradient-primary text-white">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <nav aria-label="breadcrumb" class="mb-3">
                            <ol class="breadcrumb text-white-50">
                                <li class="breadcrumb-item"><a href="index.html" class="text-white-50">首页</a></li>
                                <li class="breadcrumb-item active text-white">成功案例</li>
                            </ol>
                        </nav>
                        <h1 class="display-4 fw-bold mb-3">成功案例</h1>
                        <p class="lead mb-0">展示我们的专业实力和项目成果</p>
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <img src="images/case/case1.jpg" alt="成功案例" class="img-fluid rounded shadow" style="max-height: 200px;">
                    </div>
                </div>
            </div>
        </section>

        <!-- 搜索和筛选区域 -->
        <section class="py-4 bg-light">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="搜索案例..." id="searchInput">
                            <button class="btn btn-primary" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-lg-6 mt-3 mt-lg-0">
                        <div class="d-flex gap-2 justify-content-lg-end">
                            <button class="btn btn-outline-primary active" data-filter="all">全部</button>
                            <button class="btn btn-outline-primary" data-filter="curtain-wall">玻璃幕墙</button>
                            <button class="btn btn-outline-primary" data-filter="window">铝合金门窗</button>
                            <button class="btn btn-outline-primary" data-filter="commercial">商业建筑</button>
                            <button class="btn btn-outline-primary" data-filter="residential">住宅建筑</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 案例展示区域 -->
        <section class="py-5">
            <div class="container">
                <div class="row g-4" id="casesContainer">
                    <!-- 案例1 -->
                    <div class="col-lg-4 col-md-6 case-item" data-category="curtain-wall commercial">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <img src="images/case/case1.jpg" class="card-img-top" alt="重庆某商业综合体" style="height: 250px; object-fit: cover;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-primary">玻璃幕墙</span>
                                    <small class="text-muted">2024-03-15</small>
                                </div>
                                <h5 class="card-title fw-bold">重庆某商业综合体</h5>
                                <p class="card-text text-muted">大型商业综合体玻璃幕墙工程，采用单元式幕墙系统，总面积约15000平方米。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>重庆市渝中区
                                    </small>
                                    <a href="case-detail.html?id=1" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 案例2 -->
                    <div class="col-lg-4 col-md-6 case-item" data-category="window commercial">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <img src="images/case/case2.jpg" class="card-img-top" alt="高端办公楼项目" style="height: 250px; object-fit: cover;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-success">铝合金门窗</span>
                                    <small class="text-muted">2024-02-20</small>
                                </div>
                                <h5 class="card-title fw-bold">高端办公楼项目</h5>
                                <p class="card-text text-muted">现代化办公楼铝合金门窗工程，注重节能环保和舒适性，共计800套门窗。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>重庆市江北区
                                    </small>
                                    <a href="case-detail.html?id=2" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 案例3 -->
                    <div class="col-lg-4 col-md-6 case-item" data-category="window residential">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <img src="images/case/case3.jpg" class="card-img-top" alt="高档住宅小区" style="height: 250px; object-fit: cover;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-success">铝合金门窗</span>
                                    <small class="text-muted">2024-01-10</small>
                                </div>
                                <h5 class="card-title fw-bold">高档住宅小区</h5>
                                <p class="card-text text-muted">高档住宅小区门窗工程，提供安全舒适的居住环境，共计1200套门窗。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>重庆市南岸区
                                    </small>
                                    <a href="case-detail.html?id=3" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 案例4 -->
                    <div class="col-lg-4 col-md-6 case-item" data-category="curtain-wall commercial">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <img src="images/case/case4.jpg" class="card-img-top" alt="文化中心幕墙" style="height: 250px; object-fit: cover;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-primary">玻璃幕墙</span>
                                    <small class="text-muted">2023-12-05</small>
                                </div>
                                <h5 class="card-title fw-bold">文化中心幕墙</h5>
                                <p class="card-text text-muted">文化场馆玻璃幕墙，结合建筑特色设计，采用点支式幕墙系统。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>重庆市沙坪坝区
                                    </small>
                                    <a href="case-detail.html?id=4" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 案例5 -->
                    <div class="col-lg-4 col-md-6 case-item" data-category="curtain-wall commercial">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <img src="images/case/case5.jpg" class="card-img-top" alt="医院幕墙系统" style="height: 250px; object-fit: cover;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-primary">玻璃幕墙</span>
                                    <small class="text-muted">2023-11-18</small>
                                </div>
                                <h5 class="card-title fw-bold">医院幕墙系统</h5>
                                <p class="card-text text-muted">医疗建筑玻璃幕墙，注重功能性和安全性，采用框架式幕墙系统。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>重庆市九龙坡区
                                    </small>
                                    <a href="case-detail.html?id=5" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 案例6 -->
                    <div class="col-lg-4 col-md-6 case-item" data-category="window commercial">
                        <div class="card border-0 shadow-sm scale-on-hover">
                            <img src="images/case/case7.jpg" class="card-img-top" alt="酒店门窗工程" style="height: 250px; object-fit: cover;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-success">铝合金门窗</span>
                                    <small class="text-muted">2023-10-25</small>
                                </div>
                                <h5 class="card-title fw-bold">酒店门窗工程</h5>
                                <p class="card-text text-muted">高档酒店铝合金门窗工程，展现建筑的豪华与现代感。</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>重庆市渝北区
                                    </small>
                                    <a href="case-detail.html?id=6" class="btn btn-outline-primary btn-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页导航 -->
                <nav aria-label="案例分页" class="mt-5">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <li class="page-item active">
                            <a class="page-link" href="#">1</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </section>

        <!-- 联系咨询区域 -->
        <section class="py-5 bg-gradient-primary text-white">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h3 class="fw-bold mb-3">想要了解更多项目案例？</h3>
                        <p class="lead mb-0">联系我们，让我们为您提供更多成功案例和专业服务</p>
                    </div>
                    <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                        <div class="d-flex gap-3 justify-content-lg-end">
                            <a href="contact.html" class="btn btn-light btn-lg">
                                <i class="fas fa-phone me-2"></i>立即咨询
                            </a>
                            <a href="tel:023-12345678" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-mobile-alt me-2"></i>电话咨询
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3">重庆锦雨丰建筑有限公司</h5>
                    <p class="text-muted">致力于为客户提供高品质建筑服务的综合性企业，专业从事玻璃幕墙和铝合金门窗的设计、制造与安装。</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-weixin"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-qq"></i></a>
                        <a href="#" class="text-light"><i class="fas fa-phone"></i></a>
                        <a href="#" class="text-light"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-bold mb-3">产品服务</h6>
                    <ul class="list-unstyled">
                        <li><a href="glass-curtain-wall.html" class="text-muted text-decoration-none">玻璃幕墙</a></li>
                        <li><a href="aluminium-window.html" class="text-muted text-decoration-none">铝合金门窗</a></li>
                        <li><a href="cases.html" class="text-muted text-decoration-none">成功案例</a></li>
                        <li><a href="videos.html" class="text-muted text-decoration-none">视频中心</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-bold mb-3">公司信息</h6>
                    <ul class="list-unstyled">
                        <li><a href="about.html" class="text-muted text-decoration-none">关于我们</a></li>
                        <li><a href="news.html" class="text-muted text-decoration-none">资讯动态</a></li>
                        <li><a href="contact.html" class="text-muted text-decoration-none">联系我们</a></li>
                        <li><a href="search.html" class="text-muted text-decoration-none">站内搜索</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h6 class="fw-bold mb-3">联系方式</h6>
                    <ul class="list-unstyled text-muted">
                        <li><i class="fas fa-map-marker-alt me-2"></i>重庆市渝北区某某街道123号</li>
                        <li><i class="fas fa-phone me-2"></i>023-12345678</li>
                        <li><i class="fas fa-mobile-alt me-2"></i>138-0000-0000</li>
                        <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 重庆锦雨丰建筑有限公司. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex gap-3 justify-content-md-end">
                        <a href="#" class="text-muted text-decoration-none small">隐私政策</a>
                        <a href="#" class="text-muted text-decoration-none small">服务条款</a>
                        <a href="#" class="text-muted text-decoration-none small">网站地图</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 回到顶部按钮 -->
    <button class="btn btn-primary back-to-top" id="backToTop" title="回到顶部">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Bootstrap JavaScript -->
    <script src="libs/bootstrap.bundle.min.js"></script>
    <!-- 自定义JavaScript -->
    <script src="js/main.js"></script>

    <!-- 案例筛选和搜索功能 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('[data-filter]');
            const caseItems = document.querySelectorAll('.case-item');
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');

            // 筛选功能
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');

                    // 更新按钮状态
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // 筛选案例
                    caseItems.forEach(item => {
                        if (filter === 'all' || item.getAttribute('data-category').includes(filter)) {
                            item.style.display = 'block';
                            item.classList.add('fade-in-up');
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });

            // 搜索功能
            function performSearch() {
                const searchTerm = searchInput.value.toLowerCase();

                caseItems.forEach(item => {
                    const title = item.querySelector('.card-title').textContent.toLowerCase();
                    const description = item.querySelector('.card-text').textContent.toLowerCase();

                    if (title.includes(searchTerm) || description.includes(searchTerm)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            }

            searchBtn.addEventListener('click', performSearch);
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });
    </script>
